services:
  jetbrains-proxy:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: jetbrains-proxy
    ports:
      - "8501:8501"
    volumes:
      - ./config.json:/app/config.json:ro
      - ./jetbrainsai.json:/app/jetbrainsai.json
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # JetBrains AI to API 服務 - 獨立服務，只共用 jetbrainsai.json
  # jetbrainsai2api:
  #   build: .
  #   container_name: jetbrainsai2api
  #   ports:
  #     - "8000:8000"
  #   volumes:
  #     - ./jetbrainsai.json:/app/jetbrainsai.json:ro
  #     - ./client_api_keys.json:/app/client_api_keys.json:ro
  #     - ./models.json:/app/models.json:ro
  #   environment:
  #     - DEBUG_MODE=${DEBUG_MODE:-false}
  #   restart: unless-stopped

networks:
  default:
    name: jetbrains-network
